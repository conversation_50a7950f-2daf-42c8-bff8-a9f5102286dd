import { ApiServiceInstance } from '.';
import { API_ROUTES } from '@/utils/constants';

const { CREATE, GET_ALL, GET_BY_ID, GET_ACTIVE_JOBS } = API_ROUTES.JOB;
export const createJob = async (
  payload: JobInformation
): Promise<IResponseData<JobInformationResponse>> => {
  return ApiServiceInstance.callPostApi<JobInformationResponse, JobInformation>(CREATE, payload);
};
export const getJobs = async (): Promise<IResponseData<JobList[]>> => {
  return ApiServiceInstance.callGetApi<JobList[]>(GET_ALL);
};

export const getActiveJobs = async (): Promise<IResponseData<ActiveJob[]>> => {
  return ApiServiceInstance.callGetApi<ActiveJob[]>(GET_ACTIVE_JOBS);
};

export const getJob = async (id: string): Promise<IResponseData<JobInformationResponse>> => {
  return ApiServiceInstance.callGetApi<JobInformationResponse>(GET_BY_ID(id));
};
// export const updateJob = async (
//   id: string,
//   payload: IUpdateJobRequest
// ): Promise<IResponseData<IJob>> => {
//   return ApiServiceInstance.callPutApi<IJob, IUpdateJobRequest>(UPDATE_JOB(id), payload);
// };
// export const deleteJob = async (id: string): Promise<IResponseData<IJob>> => {
//   return ApiServiceInstance.callDeleteApi<IJob>(DELETE_JOB(id));
// };
