import { ApiServiceInstance } from '.';
import { API_ROUTES } from '@/utils/constants';

const { APPLY_JOB, VERIFY_EMAIL, COMPLETE_PROFILE } = API_ROUTES.CANDIDATE;

export const applyForJob = async (data: JobApplyRequest): Promise<IResponseData<string>> => {
  return ApiServiceInstance.callPostApi<string, JobApplyPayload>(
    APPLY_JOB(data.jobId),
    data.payload
  );
};

export const profileCreate = async (data: {
  jobId: string;
  payload: CandidateProfileCreatePayload;
}): Promise<IResponseData<CandidateProfileCreateResponse>> => {
  return ApiServiceInstance.callPostApi<
    CandidateProfileCreateResponse,
    CandidateProfileCreatePayload
  >(APPLY_JOB(data.jobId), data.payload);
};

export const verifyEmail = async (
  data: EmailVerificationRequest
): Promise<IResponseData<EmailVerificationResponse>> => {
  return ApiServiceInstance.callPostApi<EmailVerificationResponse, EmailVerificationRequest>(
    VERIFY_EMAIL,
    data
  );
};

export const completeProfile = async (data: {
  candidateId: string;
  payload: CandidateProfileCompletePayload;
}): Promise<IResponseData<string>> => {
  return ApiServiceInstance.callPatchApi<string, CandidateProfileCompletePayload>(
    COMPLETE_PROFILE(data.candidateId!),
    data.payload
  );
};
