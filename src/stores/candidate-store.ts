import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';

interface CandidateState {
  candidateId: string | null;
  setCandidateId: (id: string | null) => void;
  clearCandidateId: () => void;
}

export const useCandidateStore = create<CandidateState>()(
  persist(
    (set) => ({
      candidateId: null,

      setCandidateId: (id: string | null) => set({ candidateId: id }),

      clearCandidateId: () => set({ candidateId: null }),
    }),
    {
      name: 'candidate-storage',
      storage: createJSONStorage(() => sessionStorage),
    }
  )
);
