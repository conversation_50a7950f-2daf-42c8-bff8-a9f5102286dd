import { z } from 'zod';

const CreateCandidateSchema = z.object({
  full_name: z.string().min(1, 'Full name is required'),
  email: z.string().email('Invalid email address'),
  phone_number: z.string().min(1, 'Phone number is required'),
  address: z.string().min(1, 'Address is required'),
  years_of_experience: z
    .string()
    .min(1, 'Years of experience is required')
    .regex(/^\d+$/, 'Years of experience must be a positive number'),
});

export type CreateCandidateType = z.infer<typeof CreateCandidateSchema>;

export default CreateCandidateSchema;

export const emailVerificationSchema = z.object({
  full_name: z.string().min(1, 'Full name is required'),
  email: z.string().email('Invalid email address'),
  phone_number: z.string().min(1, 'Phone number is required'),
});

export const applicationDetailsSchema = z.object({
  address: z.string().min(1, 'Address is required'),
  years_of_experience: z
    .string()
    .min(1, 'Years of experience is required')
    .regex(/^\d+$/, 'Years of experience must be a positive number'),
});

export type EmailVerificationData = z.infer<typeof emailVerificationSchema>;
export type ApplicationDetailsData = z.infer<typeof applicationDetailsSchema>;
