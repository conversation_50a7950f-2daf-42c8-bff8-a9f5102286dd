import { signal, computed } from '@preact/signals-react';

export interface InterviewConfig {
  interviewId: string;
  totalDuration: number; // in seconds
}

export interface QAItem {
  question: string;
  answer: string;
}

// Core Interview State Signals
export const interviewSession = signal<InterviewConfig>({
  interviewId: '',
  totalDuration: 1800, // 30 minutes default
});

export const interviewProgress = signal({
  currentQuestionIndex: 0,
  totalQuestions: 0,
  isCompleted: false,
  completionReason: '' as 'time_up' | 'all_questions' | 'manual'
});

export const currentQuestion = signal({
  text: '',
  expectedDuration: 60,
  isLoading: false,
  questionId: ''
});

export const answerState = signal({
  currentAnswer: '',
  transcribedText: '',
  editedAnswer: '',
  isRecording: false,
  isTranscribing: false,
  recordingBlob: null as Blob | null
});

export const mediaState = signal({
  hasPermission: false,
  isVideoEnabled: true,
  isAudioEnabled: true,
  stream: null as MediaStream | null,
  error: ''
});

// Store Q&A history for API calls
export const qaHistory = signal<QAItem[]>([]);

// Computed values
export const isInterviewActive = computed(() => 
  !interviewProgress.value.isCompleted && 
  interviewSession.value.interviewId !== ''
);
