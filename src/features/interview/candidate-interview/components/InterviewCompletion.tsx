import { useSignals } from '@preact/signals-react/runtime';
import { interviewSession, interviewProgress } from '../signals/interviewSignals';
import { overallTimeFormatted } from '../signals/timerSignals';
import { CheckCircle, Clock, User } from 'lucide-react';

export function InterviewCompletion() {
  useSignals();

  const getCompletionMessage = () => {
    switch (interviewProgress.value.completionReason) {
      case 'time_up':
        return 'Interview completed - Time limit reached';
      case 'all_questions':
        return 'Interview completed - All questions answered';
      case 'manual':
        return 'Interview completed - Manually ended';
      default:
        return 'Interview completed';
    }
  };

  return (
    <div className='flex min-h-screen flex-col items-center justify-center bg-gray-50 p-6'>
      <div className='w-full max-w-2xl rounded-lg bg-white p-8 shadow-lg text-center'>
        <div className='mb-6'>
          <CheckCircle className='mx-auto h-16 w-16 text-green-500 mb-4' />
          <h1 className='text-3xl font-bold text-gray-900 mb-2'>
            Interview Complete!
          </h1>
          <p className='text-lg text-gray-600'>
            {getCompletionMessage()}
          </p>
        </div>

        <div className='grid grid-cols-1 md:grid-cols-3 gap-4 mb-8 p-4 bg-gray-50 rounded-lg'>
          <div className='text-center'>
            <Clock className='mx-auto h-8 w-8 text-blue-500 mb-2' />
            <p className='text-sm text-gray-600'>Time Remaining</p>
            <p className='font-semibold'>{overallTimeFormatted.value}</p>
          </div>
          <div className='text-center'>
            <CheckCircle className='mx-auto h-8 w-8 text-green-500 mb-2' />
            <p className='text-sm text-gray-600'>Status</p>
            <p className='font-semibold text-green-600'>Completed</p>
          </div>
        </div>

        <div className='mb-6 p-4 bg-blue-50 rounded-lg text-left'>
          <h3 className='font-semibold text-blue-900 mb-2'>What happens next?</h3>
          <ul className='text-sm text-blue-800 space-y-1'>
            <li>• Your responses have been recorded and saved</li>
            <li>• Our team will review your interview within 2-3 business days</li>
            <li>• You'll receive feedback and next steps via email</li>
            <li>• Thank you for your time and interest</li>
          </ul>
        </div>

        <div className='space-y-3'>
          <p className='text-sm text-gray-500'>
            Need help? <a href='#' className='text-blue-600 underline'>Contact support</a>
          </p>
        </div>
      </div>
    </div>
  );
}
