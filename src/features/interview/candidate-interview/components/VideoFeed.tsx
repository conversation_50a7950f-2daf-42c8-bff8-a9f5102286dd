import { useSignals } from '@preact/signals-react/runtime';
import { useEffect, useRef, useState } from 'react';
import { mediaState } from '../signals/interviewSignals';
import { mediaService } from '../services/mediaService';
import { AlertCircle } from 'lucide-react';

export function VideoFeed() {
  useSignals();
  const videoRef = useRef<HTMLVideoElement>(null);
  const [isCheckingPermissions, setIsCheckingPermissions] = useState(true);

  useEffect(() => {
    // Check for existing permissions on mount
    const checkPermissions = async () => {
      setIsCheckingPermissions(true);
      
      // If we don't have permission, check if it was granted before
      if (!mediaState.value.hasPermission) {
        const hasExistingPermission = await mediaService.checkExistingPermissions();
        if (!hasExistingPermission) {
          // Try to request permissions silently
          await mediaService.requestPermissions();
        }
      }
      
      setIsCheckingPermissions(false);
    };

    checkPermissions();
  }, []);

  useEffect(() => {
    if (mediaState.value.stream && videoRef.current) {
      videoRef.current.srcObject = mediaState.value.stream;
    }
  }, [mediaState.value.stream]);

  const handleRequestPermissions = async () => {
    setIsCheckingPermissions(true);
    await mediaService.requestPermissions();
    setIsCheckingPermissions(false);
  };

  return (
    <div className='overflow-hidden rounded-lg shadow-sm relative'>
      <video
        ref={videoRef}
        autoPlay
        muted
        className="min-h-[300px] max-h-[400px] w-full -scale-x-100 object-cover rounded-lg"
        style={{ backgroundColor: mediaState.value.hasPermission ? 'transparent' : '#000' }}
      />
      
      {/* Loading state */}
      {isCheckingPermissions && (
        <div className="absolute inset-0 flex items-center justify-center text-white bg-black bg-opacity-50 rounded-lg">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-white mx-auto mb-2"></div>
            <p>Checking camera permissions...</p>
          </div>
        </div>
      )}
      
      {/* No permission state */}
      {!mediaState.value.hasPermission && !isCheckingPermissions && (
        <div className="absolute inset-0 flex items-center justify-center text-white bg-black bg-opacity-50 rounded-lg">
          <div className="text-center">
            <AlertCircle className="mx-auto mb-2 h-8 w-8" />
            <p className="mb-2">Camera not available</p>
            <button 
              onClick={handleRequestPermissions}
              className="px-3 py-1 bg-blue-600 text-white rounded text-sm hover:bg-blue-700"
            >
              Enable Camera
            </button>
            {mediaState.value.error && (
              <p className="text-xs mt-2 text-red-300">{mediaState.value.error}</p>
            )}
          </div>
        </div>
      )}
    </div>
  );
}
