import { speechService } from '../services/speechService';
import { timerService } from '../services/timerService';
import {
  currentQuestion,
  interviewSession,
  interviewProgress,
  qaHistory,
} from '../signals/interviewSignals';
import {
  currentPhase,
  InterviewPhase,
  canRequestNewQuestion,
  overallTimeRemaining,
} from '../signals/timerSignals';
import PreviaAvatar from '@/assets/icons/previa-avatar';
import { useQuestionWebSocket, useGenerateQuestionAPIMutation } from '@/hooks/api/use-interview';
import { useSignals } from '@preact/signals-react/runtime';
import { useEffect, useState, useRef } from 'react';

export function QuestionDisplay() {
  useSignals();
  const [isLoadingQuestion, setIsLoadingQuestion] = useState(false);
  const [hasInitialQuestionRequested, setHasInitialQuestionRequested] = useState(false);
  const audioRef = useRef<HTMLAudioElement>(null);
  
  const { isConnected, lastMessage, channelId } = useQuestionWebSocket();
  const generateQuestionMutation = useGenerateQuestionAPIMutation();

  // Request initial question when WebSocket connects
  useEffect(() => {
    if (isConnected && channelId && !hasInitialQuestionRequested) {
      requestQuestion();
      setHasInitialQuestionRequested(true);
    }
  }, [isConnected, channelId, hasInitialQuestionRequested]);

  useEffect(() => {
    if (currentPhase.value === InterviewPhase.LOADING_QUESTION && canRequestNewQuestion.value) {
      requestQuestion();
    }
  }, [currentPhase.value, canRequestNewQuestion.value]);

  // Handle WebSocket messages
  useEffect(() => {
    if (!lastMessage) return;

    if (lastMessage.type === 'text' && lastMessage.content) {
      const questionId = `q_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      
      currentQuestion.value = {
        text: lastMessage.content,
        expectedDuration: 60,
        isLoading: false,
        questionId: questionId,
      };

      currentPhase.value = InterviewPhase.READING_QUESTION;
      setIsLoadingQuestion(false);
    }

    if (lastMessage.type === 'audio' && lastMessage.audio_data) {
      playAudioFromBase64(lastMessage.audio_data);
    }
  }, [lastMessage]);

  const playAudioFromBase64 = async (base64Audio: string) => {
    try {
      const audioBlob = base64ToBlob(base64Audio, 'audio/wav');
      const audioUrl = URL.createObjectURL(audioBlob);
      
      if (audioRef.current) {
        audioRef.current.src = audioUrl;
        await audioRef.current.play();
        
        audioRef.current.onended = () => {
          URL.revokeObjectURL(audioUrl);
          timerService.startThinkingTimer();
        };
      }
    } catch (error) {
      console.error('Failed to play TTS audio:', error);
      timerService.startThinkingTimer();
    }
  };

  const base64ToBlob = (base64: string, mimeType: string): Blob => {
    const byteCharacters = atob(base64);
    const byteNumbers = new Array(byteCharacters.length);
    for (let i = 0; i < byteCharacters.length; i++) {
      byteNumbers[i] = byteCharacters.charCodeAt(i);
    }
    const byteArray = new Uint8Array(byteNumbers);
    return new Blob([byteArray], { type: mimeType });
  };

  const requestQuestion = async () => {
    if (!isConnected || !channelId) {
      console.log('isConnected', isConnected);
      console.log('channelId', channelId);
      console.error('WebSocket not connected or no channel ID');
      return;
    }

    setIsLoadingQuestion(true);

    try {
      const lastQA = qaHistory.value[qaHistory.value.length - 1];
      
      const payload = {
        channel_id: channelId,
        chat_id: interviewSession.value.interviewId,
        //remove this
        prompt_text: `You are interviewing for Vivasoft's AI/ML Intern position in Nepal Branch. Be curious, encouraging, and Feynman-inspired - focus on HOW they think, not perfect answers.

## Core Approach
- **Conversational, not interrogative** - make it feel like a chat between curious minds
- **Celebrate learning** - if they don't know something, ask how they'd figure it out
- **Focus on potential** - this is an intern role, expect curiosity over expertise

## Question Categories & Examples

**Curiosity/Learning:**
- "What AI concept excited you recently and why?"
- "Explain neural networks to a friend who's never heard of them"

**Problem-Solving:**
- "Your chatbot gives weird responses. How do you debug it?"
- "To predict house prices, what data would you collect first?"

**Technical (CV-based):**
- "Tell me about [their project]. What was tricky?"
- "What confused you about [framework/tool] initially?"

**LLM/Modern AI:**
- "What makes ChatGPT work so well in your opinion?"
- "Building a student assistant - what's your biggest challenge?"

## Tone Examples
- Start: "Hey [name], this is more conversation than formal interview!"
- Use: "Great thinking!", "What's your intuition?", "Don't worry, what would you try first?"
- If stuck: "No worries! What would you Google?"

## Look For
- Curiosity and learning enthusiasm
- Clear communication of ideas
- Logical problem-solving approach
- Humility about unknowns
- Questions back to you

## Avoid
- Gotcha questions or trying to trip them up
- Expecting perfect technical knowledge
- Making them feel bad for not knowing

**Interview Flow:** Warm-up → Technical exploration → Problem-solving → LLM discussion → Wrap-up (25-30 min total)

Remember: We want someone who thinks like Feynman—curious, humble, and excited to learn. Look for growth potential, not perfection.`,
        cv_link: "production/test_job_001_cvs/Resume___Afrin_Sultana_Poushi-b97def2c7a1748bbb1af790a0153c921.pdf",
        ...(lastQA && {
          last_question_answer: {
            chat_id: interviewSession.value.interviewId,
            answer: lastQA.answer
          }
        })
      };

      await generateQuestionMutation.mutateAsync(payload);
      console.log('Question generation request sent');
    } catch (error) {
      console.error('Failed to request question:', error);
      setIsLoadingQuestion(false);
    }
  };

  const showCompletionMessage = () => {
    const completionMessage = `Thank you for participating in this interview. Your responses have been recorded and our team will review them shortly.`;

    currentQuestion.value = {
      ...currentQuestion.value,
      text: completionMessage,
      isLoading: false,
    };
  };

  const getDisplayContent = () => {
    if (interviewProgress.value.isCompleted || !canRequestNewQuestion.value) {
      return {
        speaker: 'Previa',
        message: currentQuestion.value.text || `Thank you for participating in this interview.`,
      };
    }

    if (isLoadingQuestion || currentPhase.value === InterviewPhase.LOADING_QUESTION) {
      return {
        speaker: 'Previa',
        message: 'Generating your next question...',
      };
    }

    return {
      speaker: 'Previa',
      message: currentQuestion.value.text || 'Welcome to your interview!',
    };
  };

  const content = getDisplayContent();

    return (
    <div className='flex items-start gap-6'>
      <audio ref={audioRef} style={{ display: 'none' }} />
      <div className='flex-shrink-0'>
        <div className='border-primary flex size-28 flex-col items-center justify-center gap-5 rounded-2xl border bg-gray-50'>
          <PreviaAvatar className='size-10' />
          <div className='flex w-full items-center justify-around'>
            <span className='text-xs font-medium text-gray-700'>Previa</span>
            <div className='mt-1 flex animate-pulse space-x-1'>
              {Array.from({ length: 3 }).map((_, index) => (
                <div key={index} className='size-1 rounded-full bg-blue-400' />
              ))}
            </div>
          </div>
        </div>
      </div>
      <div className='flex-1'>
        {/* <div className='mb-4'>
          <span className='text-lg leading-relaxed font-medium text-gray-900'>
            {content.speaker}
          </span>
        </div> */}
        <div className='rounded-lg p-4'>
          <p className='font-semibold leading-relaxed text-gray-800'>{content.message}</p>
        </div>
      </div>
    </div>
  );
}
