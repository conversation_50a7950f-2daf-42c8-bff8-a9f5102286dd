import {
  MediaDevice,
  mediaService,
} from '@/features/interview/candidate-interview/services/mediaService';
import { mediaState } from '@/features/interview/candidate-interview/signals/interviewSignals';
import { usePrepareInterviewMutation } from '@/hooks/api/use-interview';
import { PrepareInterviewResponse } from '@/services/interview';
import SessionStorageManager, { SESSION_STORAGE_KEYS } from '@/utils/sessionStorage';
import { useSignals } from '@preact/signals-react/runtime';
import { useNavigate } from '@tanstack/react-router';
import { AlertCircle, AlertTriangle, CheckCircle } from 'lucide-react';
import { useEffect, useRef, useState } from 'react';
import { toast } from 'sonner';

const InterviewOnboardingSystemSetup = () => {
  useSignals();
  const navigate = useNavigate();
  const videoRef = useRef<HTMLVideoElement>(null);
  const prepareInterviewMutation = usePrepareInterviewMutation();

  const [selectedCamera, setSelectedCamera] = useState('');
  const [selectedMicrophone, setSelectedMicrophone] = useState('');
  const [availableDevices, setAvailableDevices] = useState<{
    cameras: MediaDevice[];
    microphones: MediaDevice[];
  }>({ cameras: [], microphones: [] });
  const [isRequestingPermission, setIsRequestingPermission] = useState(false);
  const [isLoadingDevices, setIsLoadingDevices] = useState(false);
  const [isPreparing, setIsPreparing] = useState(false);
  const candidateData = SessionStorageManager.getItem<{ candidate_id: string }>(
    SESSION_STORAGE_KEYS.CANDIDATE_DATA
  );
  const candidate_id = candidateData?.candidate_id;

  useEffect(() => {
    // Check browser compatibility first
    if (!mediaService.isMediaSupported()) {
      mediaState.value = {
        ...mediaState.value,
        error:
          'Your browser does not support camera and microphone access. Please use a modern browser like Chrome, Firefox, or Safari.',
      };
      return;
    }

    if (!mediaService.isSecureContext()) {
      mediaState.value = {
        ...mediaState.value,
        error:
          'Camera and microphone access requires a secure connection (HTTPS). Please ensure you are using HTTPS.',
      };
      return;
    }

    // Check for existing permissions first
    const checkExistingPermissions = async () => {
      const hasPermission = await mediaService.checkExistingPermissions();
      if (!hasPermission) {
        // Auto-request permissions if not already granted
        handleRequestPermissions();
      }
    };

    checkExistingPermissions();
  }, []);

  useEffect(() => {
    // Update video element when stream is available
    if (mediaState.value.stream && videoRef.current) {
      videoRef.current.srcObject = mediaState.value.stream;
    }
  }, [mediaState.value.stream]);

  useEffect(() => {
    // Load available devices when permission is granted
    if (mediaState.value.hasPermission) {
      loadAvailableDevices();
    }
  }, [mediaState.value.hasPermission]);

  const handleRequestPermissions = async () => {
    setIsRequestingPermission(true);
    const success = await mediaService.requestPermissions();
    setIsRequestingPermission(false);

    if (success) {
      loadAvailableDevices();
    }
  };

  const loadAvailableDevices = async () => {
    setIsLoadingDevices(true);
    try {
      const devices = await mediaService.getAvailableDevices();
      setAvailableDevices(devices);

      // Set default selections
      if (devices.cameras.length > 0 && !selectedCamera) {
        setSelectedCamera(devices.cameras[0].deviceId);
      }
      if (devices.microphones.length > 0 && !selectedMicrophone) {
        setSelectedMicrophone(devices.microphones[0].deviceId);
      }
    } catch (error) {
      console.error('Failed to load devices:', error);
    } finally {
      setIsLoadingDevices(false);
    }
  };

  const handleCameraChange = async (deviceId: string) => {
    setSelectedCamera(deviceId);
    if (mediaState.value.hasPermission) {
      await mediaService.switchCamera(deviceId);
    }
  };

  const handleMicrophoneChange = async (deviceId: string) => {
    setSelectedMicrophone(deviceId);
    if (mediaState.value.hasPermission) {
      await mediaService.switchMicrophone(deviceId);
    }
  };

  const handleProceedToInterview = async () => {
    if (!mediaState.value.hasPermission) {
      handleRequestPermissions();
      return;
    }

    setIsPreparing(true);

    try {
      const candidate_id = 'b7c3b976-4b13-408a-87a3-647864aa2474'

      if (candidate_id) {
        const prepareResponse: PrepareInterviewResponse =
          await prepareInterviewMutation.mutateAsync({ candidate_id });

        // Store candidate data with proper structure
        SessionStorageManager.setItem(SESSION_STORAGE_KEYS.CANDIDATE_DATA, {
          candidate_id: prepareResponse.candidate_id,
          job_id: prepareResponse.job_id,
        });

        // Store interview items array
        SessionStorageManager.setItem(
          SESSION_STORAGE_KEYS.INTERVIEW_ITEMS,
          prepareResponse.interview_items
        );

        // Navigate to interview page after successful preparation
        navigate({ to: '/interview' });
      }
      else {
        toast.error('Failed to prepare interview. Please verify your invitation link or Contact Us.');
      }
    } catch (error) {
      console.error('Failed to prepare interview:', error);
    } finally {
      setIsPreparing(false);
    }
  };

  const getPermissionStatus = () => {
    if (isRequestingPermission) {
      return { icon: AlertCircle, color: 'text-yellow-500', text: 'Requesting...' };
    }
    if (mediaState.value.hasPermission) {
      return { icon: CheckCircle, color: 'text-green-500', text: 'Granted' };
    }
    if (mediaState.value.error) {
      return { icon: AlertTriangle, color: 'text-red-500', text: 'Error' };
    }
    return { icon: AlertCircle, color: 'text-red-500', text: 'Denied' };
  };

  const permissionStatus = getPermissionStatus();
  const PermissionIcon = permissionStatus.icon;

  // Show browser compatibility error
  if (!mediaService.isMediaSupported() || !mediaService.isSecureContext()) {
    return (
      <div className='flex h-[calc(100vh-72px-24px-24px)] items-center justify-center bg-white'>
        <div className='max-w-md p-6 text-center'>
          <AlertTriangle className='mx-auto mb-4 h-16 w-16 text-red-500' />
          <h2 className='mb-2 text-xl font-semibold text-gray-900'>Browser Compatibility Issue</h2>
          <p className='mb-4 text-gray-600'>{mediaState.value.error}</p>
          <div className='text-sm text-gray-500'>
            <p>Supported browsers:</p>
            <ul className='mt-2 list-inside list-disc'>
              <li>Chrome 53+</li>
              <li>Firefox 36+</li>
              <li>Safari 11+</li>
              <li>Edge 12+</li>
            </ul>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className='flex h-[calc(100vh-72px-24px-24px)] items-center justify-center bg-white'>
      <div className='flex w-full max-w-6xl flex-col items-center gap-8 p-6 md:flex-row'>
        {/* Left: Video Section */}
        <div className='flex-1 space-y-4'>
          <div className='relative overflow-hidden rounded-xl'>
            <video
              ref={videoRef}
              autoPlay
              muted
              className='mb-4 size-full h-[400px] -scale-x-100 rounded-3xl bg-black object-cover'
              style={{ backgroundColor: mediaState.value.hasPermission ? 'transparent' : '#000' }}
            />
            {!mediaState.value.hasPermission && (
              <div className='absolute inset-0 flex items-center justify-center text-white'>
                <div className='text-center'>
                  <AlertCircle className='mx-auto mb-2 h-12 w-12' />
                  <p>Camera access required</p>
                </div>
              </div>
            )}
          </div>

          {/* <div className='flex justify-center gap-4'>
            <button 
              className='rounded-full bg-blue-100 p-3 hover:bg-blue-200 disabled:opacity-50'
              disabled={!mediaState.value.hasPermission}
              onClick={() => mediaState.value = { ...mediaState.value, isVideoEnabled: !mediaState.value.isVideoEnabled }}
            >
              <VideoIcon />
            </button>
            <button 
              className='rounded-full bg-blue-100 p-3 hover:bg-blue-200 disabled:opacity-50'
              disabled={!mediaState.value.hasPermission}
              onClick={() => mediaState.value = { ...mediaState.value, isAudioEnabled: !mediaState.value.isAudioEnabled }}
            >
              <IconMicrophone />
            </button>
          </div> */}

          <p className='text-center text-sm text-gray-500'>
            <span className='font-semibold'>Tips:</span> Ensure good lighting and a tidy background
          </p>
        </div>

        {/* Right: Permission Check Section */}
        <div className='flex-1 space-y-6'>
          <div>
            <h2 className='text-2xl font-semibold text-gray-900'>Permissions & System Check</h2>
            <p className='mt-1 text-gray-600'>
              This interview is part of your application for <strong>Python Developer</strong> at{' '}
              <strong>Vivasoft Ltd.</strong>
            </p>
          </div>

          {/* Camera Dropdown */}
          <div>
            <label className='mb-1 block text-sm text-gray-700'>Camera</label>
            <div className='flex items-center gap-2'>
              <select
                value={selectedCamera}
                onChange={(e) => handleCameraChange(e.target.value)}
                disabled={!mediaState.value.hasPermission || isLoadingDevices}
                className='w-full rounded-md border p-2 focus:ring-1 focus:ring-blue-500 focus:outline-none disabled:bg-gray-100'
              >
                {isLoadingDevices ? (
                  <option>Loading cameras...</option>
                ) : availableDevices.cameras.length > 0 ? (
                  availableDevices.cameras.map((camera) => (
                    <option key={camera.deviceId} value={camera.deviceId}>
                      {camera.label}
                    </option>
                  ))
                ) : (
                  <option>No cameras found</option>
                )}
              </select>
              <PermissionIcon className={`h-5 w-5 ${permissionStatus.color}`} />
            </div>
          </div>

          {/* Microphone Dropdown */}
          <div>
            <label className='mb-1 block text-sm text-gray-700'>Microphone</label>
            <div className='flex items-center gap-2'>
              <select
                value={selectedMicrophone}
                onChange={(e) => handleMicrophoneChange(e.target.value)}
                disabled={!mediaState.value.hasPermission || isLoadingDevices}
                className='w-full rounded-md border p-2 focus:ring-1 focus:ring-blue-500 focus:outline-none disabled:bg-gray-100'
              >
                {isLoadingDevices ? (
                  <option>Loading microphones...</option>
                ) : availableDevices.microphones.length > 0 ? (
                  availableDevices.microphones.map((microphone) => (
                    <option key={microphone.deviceId} value={microphone.deviceId}>
                      {microphone.label}
                    </option>
                  ))
                ) : (
                  <option>No microphones found</option>
                )}
              </select>
              <PermissionIcon className={`h-5 w-5 ${permissionStatus.color}`} />
            </div>
          </div>

          {/* Permission Status */}
          <div
            className={`rounded-md border p-3 text-sm ${
              mediaState.value.hasPermission
                ? 'border-green-300 bg-green-100 text-green-800'
                : mediaState.value.error
                  ? 'border-red-300 bg-red-100 text-red-800'
                  : 'border-yellow-300 bg-yellow-100 text-yellow-800'
            }`}
          >
            <div className='flex items-center gap-2'>
              <PermissionIcon className={`h-4 w-4 ${permissionStatus.color}`} />
              <span className='font-medium'>Permission Status: {permissionStatus.text}</span>
            </div>
            {mediaState.value.error && (
              <div className='mt-2'>
                <p>
                  <strong>Error:</strong> {mediaState.value.error}
                </p>
                <button
                  onClick={handleRequestPermissions}
                  disabled={isRequestingPermission}
                  className='mt-2 text-red-600 underline hover:text-red-800 disabled:opacity-50'
                >
                  {isRequestingPermission ? 'Requesting...' : 'Try Again'}
                </button>
              </div>
            )}
            {mediaState.value.hasPermission && (
              <p className='mt-1 text-green-700'>
                ✓ Camera and microphone access granted. You can proceed to the interview.
              </p>
            )}
          </div>

          {/* Proceed Button */}
          <button
            onClick={handleProceedToInterview}
            disabled={isRequestingPermission || !mediaState.value.hasPermission || isPreparing}
            className='w-full rounded-md bg-blue-600 py-2 text-white hover:bg-blue-700 disabled:cursor-not-allowed disabled:opacity-50'
          >
            {isPreparing
              ? 'Preparing Interview...'
              : isRequestingPermission
                ? 'Requesting Permissions...'
                : mediaState.value.hasPermission
                  ? 'Proceed to Interview'
                  : 'Grant Permissions First'}
          </button>

          <p className='text-center text-xs text-gray-500'>
            Having trouble? Make sure you're allowing camera/microphone access when prompted.
          </p>
        </div>
      </div>
    </div>
  );
};

export default InterviewOnboardingSystemSetup;
