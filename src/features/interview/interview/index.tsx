import { InterviewSession } from '../candidate-interview/components/InterviewSession';
import { mediaService } from '../candidate-interview/services/mediaService';
import { timerService } from '../candidate-interview/services/timerService';
import { interviewSession } from '../candidate-interview/signals/interviewSignals';
import { useQuestionWebSocket } from '@/hooks/api/use-interview';
import { InterviewItem } from '@/services/interview';
import SessionStorageManager, { SESSION_STORAGE_KEYS } from '@/utils/sessionStorage';
import { useSignals } from '@preact/signals-react/runtime';
import { useEffect, useState } from 'react';

interface CandidateData {
  candidate_id: string;
  job_id: string;
}

const Interview = () => {
  useSignals();
  const [isInitializing, setIsInitializing] = useState(true);

  // Initialize WebSocket connection
  const { isConnected } = useQuestionWebSocket();

  useEffect(() => {
    const initializeInterview = async () => {
      setIsInitializing(true);

      try {
        await mediaService.checkExistingPermissions();

        // Get data from session storage with proper typing
        const candidateData = SessionStorageManager.getItem(
          SESSION_STORAGE_KEYS.CANDIDATE_DATA
        ) as CandidateData | null;
        const interviewItems = SessionStorageManager.getItem(
          SESSION_STORAGE_KEYS.INTERVIEW_ITEMS
        ) as InterviewItem[] | null;

        if (!candidateData || !interviewItems || !Array.isArray(interviewItems)) {
          console.error('Missing interview data in session storage');
          // Redirect back to setup if data is missing
          window.location.href = '/interview-onboarding-system-setup';
          return;
        }

        const totalDuration =
          interviewItems.reduce(
            (total: number, item: InterviewItem) => total + item.time_duration * 60, // Convert minutes to seconds
            0
          ) || 1800; // Default 30 minutes in seconds

        console.log('Total interview duration:', totalDuration);

        interviewSession.value = {
          interviewId: candidateData.candidate_id,
          totalDuration: totalDuration,
        };

        timerService.startOverallTimer(totalDuration);
      } catch (error) {
        console.error('Failed to initialize interview:', error);
      } finally {
        setIsInitializing(false);
      }
    };

    initializeInterview();

    return () => {
      timerService.cleanup();
    };
  }, []);

  if (isInitializing || !isConnected) {
    return (
      <div className='flex min-h-screen items-center justify-center'>
        <div className='text-center'>
          <div className='mx-auto mb-4 h-12 w-12 animate-spin rounded-full border-b-2 border-blue-600'></div>
          <p className='text-gray-600'>
            {!isConnected
              ? 'Connecting to interview service...'
              : 'Initializing your interview session...'}
          </p>
          <p className='mt-2 text-sm text-gray-500'>
            {!isConnected ? 'Establishing WebSocket connection' : 'Loading interview data'}
          </p>
        </div>
      </div>
    );
  }

  return <InterviewSession />;
};

export default Interview;
