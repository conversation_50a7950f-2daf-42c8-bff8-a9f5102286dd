const PERSONAL_EMAIL_DOMAINS = [
  'gmail.com',
  'yahoo.com',
  'hotmail.com',
  'outlook.com',
  'aol.com',
  'icloud.com',
  'me.com',
  'live.com',
  'msn.com',
  'mail.com',
  'protonmail.com',
  'yandex.com',
  'zoho.com',
];

export const isBusinessEmail = (email: string): boolean => {
  const domain = email.split('@')[1]?.toLowerCase();
  return domain ? !PERSONAL_EMAIL_DOMAINS.includes(domain) : false;
};
