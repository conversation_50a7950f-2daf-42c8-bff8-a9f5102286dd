import FileUploader from '@/components/file-uploader/FileUploader';
import { ApiServiceInstance } from '@/services';
import { API_ROUTES } from '@/utils/constants';
import { type AxiosError } from 'axios';
import React, { useState } from 'react';
import { toast } from 'sonner';

interface Props {
  jobId: string;
  cv: string | null;
  setCV: React.Dispatch<React.SetStateAction<string | null>>;
}

const ResumeUploader = ({ jobId, cv, setCV }: Props) => {
  const [isLoading, setIsLoading] = useState(false);

  const handleUploadCV = async (files: File[]) => {
    if (files.length === 0) {
      toast.error('Please select a file to upload');
      return;
    }

    setIsLoading(true);
    try {
      const formData = new FormData();
      formData.append('cv', files[0]);

      const response: IResponseData<{ cv_link: string }> = await ApiServiceInstance.callPostApi<
        { cv_link: string },
        FormData
      >(API_ROUTES.CANDIDATE.UPLOAD_CV(jobId), formData, null, 'multipart/form-data');

      setCV(response.data?.cv_link);
      toast.success('CV uploaded successfully');
    } catch (err) {
      toast.error(
        (err as AxiosError<{ message: string }>)?.response?.data?.message ?? 'Failed to upload cv'
      );
    } finally {
      setIsLoading(false);
    }
  };
  return (
    <div>
      <FileUploader
        placeholder='Drag your file or'
        placeHolder2='browse'
        supportedFormats='Max 5 MB files are allowed'
        acceptedFileTypes={{ 'application/pdf': ['.pdf'], 'application/msword': ['.doc'] }}
        onFilesUploaded={(files) => handleUploadCV(files)}
        isLoading={isLoading}
        clearFiles={!isLoading && cv === null}
      />
    </div>
  );
};

export default ResumeUploader;
