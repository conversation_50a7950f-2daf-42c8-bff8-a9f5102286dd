'use client';

import { But<PERSON> } from '@/components/ui/button';
import { Dialog, DialogContent } from '@/components/ui/dialog';
import { InputOTP, InputOTPGroup, InputOTPSlot } from '@/components/ui/input-otp';
import { useVerifyEmailMutation } from '@/hooks/api/use-candidate';
import { useCandidateStore } from '@/stores/candidate-store';
import { DialogTitle } from '@radix-ui/react-dialog';
import { AxiosError } from 'axios';
import { REGEXP_ONLY_DIGITS } from 'input-otp';
import { Loader2, Mail, ArrowRight } from 'lucide-react';
import { useState, useEffect } from 'react';

interface Props {
  isOpen: boolean;
  onClose?: () => void;
  onVerify?: (otp: string) => void;
}

export function OtpVerificationModal({ isOpen, onClose, onVerify }: Props) {
  const [value, setValue] = useState('');
  const [error, setError] = useState('');

  const { mutate: verifyEmail, isPending: isLoading } = useVerifyEmailMutation();

  const { candidateId } = useCandidateStore();

  useEffect(() => {
    if (isOpen) {
      setValue('');
      setError('');
    }
  }, [isOpen]);

  const handleVerify = async () => {
    if (value.length !== 6) return;

    setError('');

    verifyEmail(
      {
        candidate_id: candidateId ?? '',
        otp: value,
      },
      {
        onSuccess: () => {
          onVerify?.(value);
        },
        onError: (error) => {
          if (error instanceof AxiosError) {
            const errorMessage = error.response?.data?.message || 'Failed to verify email';
            setError(errorMessage);
          } else {
            setError('Failed to verify email');
          }
        },
      }
    );
  };

  // const handleResend = () => {
  //   setValue('');
  //   setError('');
  // };

  const isComplete = value.length === 6;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className='backdrop-blur-sm sm:max-w-lg' hideCloseButton>
        <DialogTitle />
        <div className='space-y-4 pt-6 pb-2 text-center'>
          <div className='from-primary-500 to-primary-600 mx-auto flex h-16 w-16 items-center justify-center rounded-2xl bg-gradient-to-br shadow-lg'>
            <Mail className='h-8 w-8 text-white' />
          </div>

          <div className='space-y-2'>
            <h2 className='text-2xl font-bold text-black'>Enter Your OTP</h2>
            <p className='text-gray-dark mx-auto max-w-md text-sm leading-relaxed'>
              We've sent a 6-digit code to your email. Please enter it below
            </p>
          </div>
        </div>

        <div className='space-y-8 py-2'>
          <div className='flex justify-center'>
            <InputOTP
              maxLength={6}
              value={value}
              onChange={(value) => {
                setValue(value);
                setError('');
              }}
              pattern={REGEXP_ONLY_DIGITS}
              disabled={isLoading}
              className='gap-3'
            >
              <InputOTPGroup className='gap-3'>
                {[0, 1, 2, 3, 4, 5].map((index) => (
                  <InputOTPSlot
                    key={index}
                    index={index}
                    className='focus:border-primary-500 focus:ring-primary-500/20 h-12 w-12 rounded-xl border-2 border-slate-200 bg-white/80 text-lg font-medium backdrop-blur-sm transition-all duration-200 hover:border-slate-300 focus:ring-2'
                  />
                ))}
              </InputOTPGroup>
            </InputOTP>
          </div>

          {error && (
            <div className='text-center'>
              <p className='text-sm font-medium text-red-600'>{error}</p>
            </div>
          )}

          <div className='space-y-4'>
            <Button
              onClick={handleVerify}
              disabled={!isComplete || isLoading}
              className='group from-primary-500 to-primary-600 w-full rounded-xl bg-gradient-to-r font-semibold text-white shadow-lg transition-all duration-200 hover:shadow-xl disabled:cursor-not-allowed disabled:opacity-50'
            >
              {isLoading ? (
                <>
                  <Loader2 className='mr-2 h-4 w-4 animate-spin' />
                  Verifying...
                </>
              ) : (
                <>
                  Verify
                  <ArrowRight className='ml-2 h-4 w-4 transition-transform duration-200 group-hover:translate-x-1' />
                </>
              )}
            </Button>

            {/* <div className='text-center'>
              <button
                onClick={handleResend}
                disabled={isLoading}
                className='hover:text-primary-800 text-gray-light text-sm font-medium underline-offset-4 transition-colors duration-200 hover:underline disabled:cursor-not-allowed disabled:opacity-50'
              >
                Didn't receive the code?{' '}
                <span className='text-primary-500 font-semibold'>Resend</span>
              </button>
            </div> */}
          </div>
        </div>

        <div className='pb-6'>
          <div className='flex justify-center space-x-1'>
            {[0, 1, 2, 3, 4, 5].map((index) => (
              <div
                key={index}
                className={`h-1 w-2 rounded-full transition-all duration-300 ${
                  index < value.length ? 'bg-primary' : 'bg-slate-200'
                }`}
              />
            ))}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
