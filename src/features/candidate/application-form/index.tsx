import JobInformation from './components/job-information';
import { JobNotFound } from './components/job-not-found';
import { OtpVerificationModal } from './components/otp-verification-modal';
import ResumeUploader from './components/resume-uploader';
import { isBusinessEmail } from './utils/helper';
import HookFormItem from '@/components/hook-form/HookFormItem';
import { Loader } from '@/components/shared/loader';
import { Button, Form, FormLabel, Input } from '@/components/ui';
import { useCompleteProfileMutation, useProfileCreateMutation } from '@/hooks/api/use-candidate';
import { useJobQuery } from '@/hooks/api/use-job';
import { useCandidateStore } from '@/stores/candidate-store';
import {
  ApplicationDetailsData,
  applicationDetailsSchema,
  EmailVerificationData,
  emailVerificationSchema,
} from '@/validations/candidateSchema';
import { zodResolver } from '@hookform/resolvers/zod';
import { useParams } from '@tanstack/react-router';
import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { toast } from 'sonner';

const JobApplicationForm = () => {
  const { jobId } = useParams({ from: '/(candidate)/job-application/$jobId/' });

  const [cv, setCv] = useState<string | null>('');
  const [showVerificationModal, setShowVerificationModal] = useState(false);
  const [currentStep, setCurrentStep] = useState<'verification' | 'details'>('verification');

  const { data, isLoading, isError } = useJobQuery(jobId);
  // const { mutate: applyForJob, isPending } = useApplyJobMutation();
  const { mutate: createProfile, isPending: isCreatingProfile } = useProfileCreateMutation();
  const { mutate: completeProfile, isPending: isCompletingProfile } = useCompleteProfileMutation();

  const { setCandidateId, candidateId } = useCandidateStore();

  const emailVerificationForm = useForm<EmailVerificationData>({
    resolver: zodResolver(emailVerificationSchema),
    defaultValues: {
      full_name: '',
      email: '',
      phone_number: '',
    },
  });

  const applicationDetailsForm = useForm<ApplicationDetailsData>({
    resolver: zodResolver(applicationDetailsSchema),
    defaultValues: {
      address: '',
      years_of_experience: '',
    },
  });

  const handleEmailVerificationProceed = async () => {
    const isValid = await emailVerificationForm.trigger();
    if (isValid) {
      const formData = emailVerificationForm.getValues();

      if (!isBusinessEmail(formData.email)) {
        toast.error(
          'Personal domains (e.g., gmail.com, yahoo.com, hotmail.com) are not accepted. Please use a business email address.'
        );
        return;
      }

      createProfile(
        {
          jobId,
          payload: {
            email: formData.email,
            full_name: formData.full_name,
            phone_number: formData.phone_number,
          },
        },
        {
          onSuccess: (data) => {
            setCandidateId(data.id);
            setShowVerificationModal(true);
          },
        }
      );
    }
  };

  const handleApplicationDetailsSubmit = (data: ApplicationDetailsData) => {
    // const emailVerificationData = emailVerificationForm.getValues();
    // const combinedData = { ...emailVerificationData, ...data };

    if (!cv) {
      toast.error('Please upload your CV');
      return;
    }

    completeProfile(
      {
        candidateId: candidateId ?? '',
        payload: {
          address: data.address,
          years_of_experience: data.years_of_experience,
          cv_link: cv,
        },
      },
      {
        onSuccess: () => {
          emailVerificationForm.reset();
          applicationDetailsForm.reset();
          setCurrentStep('verification');
          setCv('');
        },
      }
    );
  };

  if (isLoading) {
    return <Loader type='page' />;
  }

  if (isError) {
    return <JobNotFound />;
  }

  return (
    <div>
      {data && <JobInformation job={data} />}
      <section className='mx-auto my-10 max-w-200'>
        <h3 className='text-gray-dark mb-6 text-2xl'>Application Form</h3>
        <Form {...emailVerificationForm}>
          <form
            className='w-full space-y-5'
            onSubmit={emailVerificationForm.handleSubmit(handleEmailVerificationProceed)}
          >
            <HookFormItem name='full_name' label='Full Name' isRequired>
              <Input placeholder='Enter your full name' disabled={currentStep === 'details'} />
            </HookFormItem>

            <div className='grid grid-cols-2 items-baseline gap-4'>
              <HookFormItem name='email' label='Email' isRequired>
                <Input
                  placeholder='Enter your business email'
                  disabled={currentStep === 'details'}
                />
              </HookFormItem>

              <HookFormItem name='phone_number' label='Phone' isRequired>
                <Input placeholder='Enter your phone number' disabled={currentStep === 'details'} />
              </HookFormItem>
            </div>
            {currentStep === 'verification' && (
              <Button className='w-38' type='submit' loading={isCreatingProfile}>
                Proceed
              </Button>
            )}
          </form>
        </Form>

        {currentStep === 'details' && (
          <Form {...applicationDetailsForm}>
            <form
              className='mt-5 w-full space-y-5'
              onSubmit={applicationDetailsForm.handleSubmit(handleApplicationDetailsSubmit)}
            >
              <HookFormItem name='address' label='Address' isRequired>
                <Input placeholder='Enter your address' />
              </HookFormItem>

              <HookFormItem name='years_of_experience' label='Years of experience' isRequired>
                <Input type='number' placeholder='Enter your years of experience' />
              </HookFormItem>

              <div>
                <FormLabel className='mb-3'>Resume/CV</FormLabel>
                <ResumeUploader cv={cv} setCV={setCv} jobId={jobId} />
                <p className='text-gray-light mt-3 text-sm'>Only support PDF and DOC files</p>
              </div>
              <Button type='submit' className='w-40' loading={isCompletingProfile}>
                Submit application
              </Button>
            </form>
          </Form>
        )}
      </section>

      <OtpVerificationModal
        isOpen={showVerificationModal}
        onVerify={() => {
          setShowVerificationModal(false);
          setCurrentStep('details');
        }}
      />
    </div>
  );
};
export default JobApplicationForm;
