import { Card, CardContent } from '@/components/ui/card';

export function JobCardSkeleton() {
  return (
    <>
      {[...Array(3)].map((_, index) => (
        <Card className='animate-pulse rounded-2xl' key={index}>
          <CardContent className='p-8'>
            <div className='flex items-center justify-between'>
              <div className='flex flex-1 items-center gap-6'>
                <div className='h-16 w-16 rounded-2xl bg-slate-200'></div>

                <div className='min-w-0 flex-1'>
                  <div className='mb-5 flex items-center gap-4'>
                    <div className='h-8 w-48 rounded-lg bg-slate-200'></div>
                    <div className='h-7 w-24 rounded-full bg-slate-200'></div>
                  </div>

                  <div className='flex items-center gap-6'>
                    <div className='flex items-center gap-2.5 rounded-xl border border-slate-100 bg-slate-50 px-4 py-2.5'>
                      <div className='h-5 w-5 rounded-full bg-slate-200'></div>
                      <div className='h-4 w-32 rounded bg-slate-200'></div>
                    </div>
                    <div className='flex items-center gap-2.5 rounded-xl border border-slate-100 bg-slate-50 px-4 py-2.5'>
                      <div className='h-5 w-5 rounded-full bg-slate-200'></div>
                      <div className='h-4 w-36 rounded bg-slate-200'></div>
                    </div>
                  </div>
                </div>
              </div>

              <div className='flex items-center'>
                <div className='h-12 w-32 rounded-xl bg-slate-200'></div>
              </div>
            </div>
          </CardContent>
        </Card>
      ))}
    </>
  );
}
