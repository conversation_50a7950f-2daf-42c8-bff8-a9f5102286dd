import { applyForJob, completeProfile, profileCreate, verifyEmail } from '@/services/candidate';
import { useMutation } from '@tanstack/react-query';
import { AxiosError } from 'axios';
import { toast } from 'sonner';

export function useApplyJobMutation() {
  return useMutation({
    mutationFn: async (data: JobApplyRequest) => {
      const response = await applyForJob(data);
      return response.data;
    },
    onSuccess: () => {
      toast.success('Application submitted successfully!');
    },
    onError: (error: Error) => {
      if (error instanceof AxiosError) {
        const errorMessage = error.response?.data?.message || 'Failed to submit application';
        toast.error(errorMessage);
      }
    },
  });
}

export function useProfileCreateMutation() {
  return useMutation({
    mutationFn: async (data: { jobId: string; payload: CandidateProfileCreatePayload }) => {
      const response = await profileCreate(data);
      return response.data;
    },
    onError: (error: Error) => {
      if (error instanceof AxiosError) {
        const errorMessage = error.response?.data?.message || 'Failed to create profile';
        toast.error(errorMessage);
      }
    },
  });
}

export function useVerifyEmailMutation() {
  return useMutation({
    mutationFn: async (data: EmailVerificationRequest) => {
      const response = await verifyEmail(data);
      return response.data;
    },
    onError: (error: Error) => {
      return error;
    },
  });
}

export function useCompleteProfileMutation() {
  return useMutation({
    mutationFn: async (data: { candidateId: string; payload: CandidateProfileCompletePayload }) => {
      const response = await completeProfile(data);
      return response.data;
    },
    onSuccess: () => {
      toast.success('Profile completed successfully!');
    },
    onError: (error: Error) => {
      if (error instanceof AxiosError) {
        const errorMessage = error.response?.data?.message || 'Failed to complete profile';
        toast.error(errorMessage);
      }
    },
  });
}
