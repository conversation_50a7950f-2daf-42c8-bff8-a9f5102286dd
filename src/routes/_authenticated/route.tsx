import { SidebarProvider } from '@/components/ui/sidebar';
import { useUserProfileQuery } from '@/hooks/api/use-auth';
import { useAuthSlice } from '@/hooks/useAuthSlice';
import { cn } from '@/lib/utils';
import { createFileRoute, Outlet, redirect } from '@tanstack/react-router';
import Cookies from 'js-cookie';
import { Suspense } from 'react';

export const Route = createFileRoute('/_authenticated')({
  beforeLoad: ({ location }) => {
    // Check if user is authenticated
    const isAuthenticated = Cookies.get(import.meta.env.VITE_PREVIA_ADMIN_ACCESS_TOKEN_KEY!);

    if (!isAuthenticated) {
      throw redirect({
        to: '/login',
        search: {
          redirect: location.href,
        },
      });
    }
  },
  component: RouteComponent,
});

function AuthenticatedLayout() {
  const { isAuthenticated } = useAuthSlice();
  const defaultOpen = Cookies.get('sidebar_state') !== 'collapsed';

  // Fetch user profile to ensure user data is available
  const { isLoading, error } = useUserProfileQuery();

  if (!isAuthenticated()) {
    return null;
  }

  if (isLoading) {
    return (
      <div className='flex h-screen items-center justify-center'>
        <div className='border-primary h-8 w-8 animate-spin rounded-full border-b-2'></div>
      </div>
    );
  }

  if (error) {
    // This will trigger if token refresh fails
    return null;
  }

  return (
    <SidebarProvider defaultOpen={defaultOpen} className='font-inter'>
      {/* TODO: Only for demo. remove later */}
      {/* <AppSidebar /> */}

      <div
        id='content'
        className={cn(
          'ml-auto w-full max-w-full',
          'peer-data-[state=collapsed]:w-[calc(100%-var(--sidebar-width-icon)-1rem)]',
          'peer-data-[state=expanded]:w-[calc(100%-var(--sidebar-width))]',
          'sm:transition-[width] sm:duration-200 sm:ease-linear',
          'flex h-svh flex-col',
          'group-data-[scroll-locked=1]/body:h-full',
          'has-[main.fixed-main]:group-data-[scroll-locked=1]/body:h-svh'
        )}
      >
        <Outlet />
      </div>
    </SidebarProvider>
  );
}

function RouteComponent() {
  return (
    <Suspense
      fallback={
        <div className='flex h-screen items-center justify-center'>
          <div className='border-primary h-8 w-8 animate-spin rounded-full border-b-2'></div>
        </div>
      }
    >
      <AuthenticatedLayout />
    </Suspense>
  );
}
