import { DemoHeader } from '@/components/layout/demo-header';
import JobBoard from '@/features/candidate/job-board';
// import Dashboard from '@/features/dashboard';
import { createFileRoute } from '@tanstack/react-router';

export const Route = createFileRoute('/_authenticated/')({
  // component: Dashboard,

  // TODO: only for demo. remove later
  component: RouteComponent,
});

function RouteComponent() {
  return (
    <main>
      <div className='mb-16'>
        <DemoHeader />
      </div>
      <JobBoard />
    </main>
  );
}
