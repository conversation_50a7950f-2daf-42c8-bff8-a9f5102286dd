export const PAGE_LIMIT = 10;

export const API_ROUTES = {
  AUTH: {
    LOGIN: '/auth/login',
    REFRESH_TOKEN: '/auth/refresh-token',
  },

  USER: {
    PROFILE: '/users/profile',
    UPDATE_PROFILE: 'user/update-profile',
  },

  INTERVIEW: {
    CREATE_FLOW: '/api/v1/interview-configs',
    GET_ALL_FLOWS: (limit: number, offset: number) =>
      `/api/v1/interview-configs?limit=${limit}&offset=${offset}`,
    PREPARE_INTERVIEW: '/interviews/prepare',
    CREATE_USER_INTERVIEW: '/interviews',
    SUBMIT_FEEDBACK: (userInterviewId: string) => `/api/v1/interviews/${userInterviewId}/feedback`,
    GET_CANDIDATES_BY_JOB: (
      jobId: string,
      limit: number,
      offset: number,
      status: string | undefined,
      searchParam?: string
    ) =>
      `/api/v1/dashboards/jobs/${jobId}/candidates?limit=${limit}&offset=${offset}&status=${status}${searchParam}`,
    GET_CANDIDATE_EVALUATION: (jobId: string, candidateId: string) =>
      `/api/v1/dashboards/jobs/${jobId}/candidates/${candidateId}/evaluation`,
    GET_CANDIDATE_COUNT: (jobId: string) => `/api/v1/candidates/count/${jobId}`,
    GET_DASHBOARD_JOBS: '/api/v1/dashboards/jobs',
    RESEND_INVITATION: '/api/v1/interviews/resend-invitation',
  },

  PROMPT: {
    CREATE: '/api/v1/agents',
    GET_ALL: '/api/v1/agents',
    GET_BY_ID: (agentId: string) => `/api/v1/agents/${agentId}`,
    GENERATE: '/api/v1/agents/generate-prompt',
    LIVE_PREVIEW: '/live-interview/next-question',
    LIVE_EVALUATION: '/api/v1/live-evaluation',
    PREVIEW_CV: (cvLink: string) => `/api/v1/candidates/preview-cv?cv_link=${cvLink}`,
  },

  JOB: {
    CREATE: '/api/v1/jobs',
    GET_ALL: '/api/v1/jobs',
    GET_ACTIVE_JOBS: '/jobs/active',
    GET_BY_ID: (jobId: string) => `/api/v1/jobs/${jobId}`,
  },

  CANDIDATE: {
    CREATE: '/candidates',
    VERIFY: '/candidates/verify',
    UPLOAD_CV: (jobId: string) => `/jobs/${jobId}/candidates/upload-cv`,
    APPLY_JOB: (jobId: string) => `/jobs/${jobId}/candidates`,
    VERIFY_EMAIL: '/candidates/verify-email',
    COMPLETE_PROFILE: (candidateId: string) => `/candidates/${candidateId}/complete`,
  },

  STT: {
    TRANSCRIBE_SYNC: '/transcribe/sync',
  },
  QUESTION: {
    GENERATE: '/question-engine/generate-question',
  },
};
